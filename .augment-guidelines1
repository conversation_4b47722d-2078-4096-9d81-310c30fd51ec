请作为编排器角色，对目标URL进行完整的自动化检测和分析，然后生成一个符合executor.py要求的YAML格式执行流模板文件。具体要求如下：

**1. 自动化检测任务：**
- 使用Playwright自动化工具访问目标
- 检查页面是否存在登录表单
- 识别并记录所有登录相关的页面元素（用户名输入框、密码输入框、登录按钮等）
- 获取元素的准确选择器（ID、CSS选择器、name属性等）

**2. 登录流程测试：**
- 使用符合账号密码策略的随机账号密码（勿使用常见弱口令）进行一次完整的登录尝试
- 观察登录成功和失败时的页面响应和元素变化
- 识别登录失败的判断条件（如错误提示信息、页面状态等）
- 记录网络请求响应状态码

**3. 生成YAML模板文件：**
- 模板文件保存目录：`templates/`
- 模板必须与现有的executor.py代码兼容
- 参考模板文件保存目录下现有的模板格式
- 包含以下必需字段：
- `name`: 模板名称
- `description`: 模板描述
- `variables`: [username, password]
- `failure_condition`: 失败条件（仅支持element_is_visible类型）
- `steps`: 操作步骤数组

**4. 模板内容要求：**
- 支持的操作类型：navigate、type、click
- 支持的选择器类型：selector、role
- 失败条件必须基于页面可见元素（文本内容）
- 所有选择器必须经过实际测试验证
- 参数化配置支持{username}和{password}变量替换

**5. 质量保证：**
- 对生成的模板进行自查，确保选择器准确性
- 验证失败条件能正确识别登录失败情况
- 确保模板格式符合YAML语法规范
- 避免使用executor.py不支持的功能（如network_response、url_change等）
- 最终只需生成模板文件，不需要检查和生成字典文件

请完成上述检测分析后，生成最终的YAML模板文件。