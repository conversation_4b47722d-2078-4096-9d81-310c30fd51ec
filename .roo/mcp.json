{"mcpServers": {"Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "alwaysAllow": ["browser_close", "browser_type", "browser_resize", "browser_console_messages", "browser_handle_dialog", "browser_evaluate", "browser_file_upload", "browser_press_key", "browser_wait_for", "browser_tab_close", "browser_tab_select", "browser_tab_new", "browser_tab_list", "browser_select_option", "browser_hover", "browser_drag", "browser_click", "browser_snapshot", "browser_take_screenshot", "browser_network_requests", "browser_navigate_forward", "browser_navigate_back", "browser_install", "browser_navigate"], "disabled": false}}}